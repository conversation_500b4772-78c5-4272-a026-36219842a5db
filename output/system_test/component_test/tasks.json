{"last_saved": "2025-06-16T01:34:24.802884", "tasks": [{"id": "task-6b8593e0", "priority": 1, "description": "Complete code review for PR #123", "status": "pending", "created_at": "2025-06-16T01:34:20.862111", "completed_at": null}, {"id": "task-b7075ddd", "priority": 2, "description": "Write unit tests for new feature", "status": "pending", "created_at": "2025-06-16T01:34:20.862252", "completed_at": null}, {"id": "task-86da772d", "priority": 2, "description": "Refactor legacy authentication code", "status": "pending", "created_at": "2025-06-16T01:34:20.862477", "completed_at": null}, {"id": "task-92ce67f6", "priority": 3, "description": "Update API documentation", "status": "pending", "created_at": "2025-06-16T01:34:20.862376", "completed_at": null}, {"id": "task-f6219d4a", "priority": 3, "description": "Prepare sprint retrospective notes", "status": "pending", "created_at": "2025-06-16T01:34:20.862578", "completed_at": null}]}