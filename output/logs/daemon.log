╭───────────────────────────╮
│ Productivity Guard        │
│ AI-driven focus companion │
╰───────────────────────────╯

Starting monitoring system...
Screenshot interval: 120s
AI check interval: 300s
Output directory: output
2025-06-16 01:54:29,362 - src.main_app - INFO - Initializing Productivity Guard components...
2025-06-16 01:54:29,362 - src.task_manager.manager - INFO - Loaded 11 tasks from output/tasks.json
2025-06-16 01:54:29,362 - src.local_store.store - INFO - LocalStore initialized with base path: output
2025-06-16 01:54:29,362 - src.main_app - INFO - All components initialized successfully

System ready! Press Ctrl+C to stop.

2025-06-16 01:54:29,363 - src.main_app - INFO - Starting Productivity Guard...
2025-06-16 01:54:29,363 - src.main_app - INFO - Entering main monitoring loop
2025-06-16 01:54:29,879 - src.reasoning_ai.reasoner - INFO - Mock Analysis: ON-TASK (confidence: 0.90)
2025-06-16 01:54:29,880 - src.main_app - INFO - User is on-task (confidence: 0.90)
2025-06-16 01:54:30,295 - src.main_app - ERROR - Failed to capture screenshot from screen 3: Invalid screen_id 3, must be 1-2
2025-06-16 01:54:30,302 - src.main_app - ERROR - Failed to capture screenshot from screen 4: Invalid screen_id 4, must be 1-2
