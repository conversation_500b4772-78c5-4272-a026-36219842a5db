2025-06-16 01:57:47,430 - src.main_app - INFO - Initializing Productivity Guard components...
2025-06-16 01:57:47,430 - src.task_manager.manager - INFO - Loaded 11 tasks from output/tasks.json
2025-06-16 01:57:47,430 - src.local_store.store - INFO - LocalStore initialized with base path: output
2025-06-16 01:57:47,430 - src.main_app - INFO - All components initialized successfully
2025-06-16 01:57:47,431 - src.task_manager.manager - INFO - Added task task-dedd1126: Review pull request #123...
2025-06-16 01:57:47,431 - src.task_manager.manager - INFO - Added task task-6d46ad76: Implement user authentication...
2025-06-16 01:57:47,431 - src.task_manager.manager - INFO - Added task task-e0364897: Write unit tests for API endpoints...
2025-06-16 01:57:47,432 - src.task_manager.manager - INFO - Added task task-58e3da3c: Update project documentation...
2025-06-16 01:57:47,433 - src.task_manager.manager - INFO - Added task task-749f532e: Refactor database queries...
2025-06-16 01:57:47,433 - src.main_app - INFO - Starting Productivity Guard...
2025-06-16 01:57:47,433 - src.main_app - INFO - Entering main monitoring loop
2025-06-16 01:57:47,957 - src.reasoning_ai.reasoner - INFO - Mock Analysis: ON-TASK (confidence: 0.90)
2025-06-16 01:57:47,958 - src.main_app - INFO - User is on-task (confidence: 0.90)
2025-06-16 01:57:48,401 - src.main_app - ERROR - Failed to capture screenshot from screen 3: Invalid screen_id 3, must be 1-2
2025-06-16 01:57:48,401 - src.main_app - ERROR - Failed to capture screenshot from screen 4: Invalid screen_id 4, must be 1-2
