{"tasks": [{"id": "task-d3928a88", "priority": 1, "description": "Complete PRD documentation", "status": "completed", "created_at": "2025-06-16T00:24:42.402687", "completed_at": "2025-06-16T00:24:42.403859"}, {"id": "task-816da690", "priority": 2, "description": "Review code implementation", "status": "pending", "created_at": "2025-06-16T00:24:42.402877", "completed_at": null}, {"id": "task-822cd577", "priority": 3, "description": "Run integration tests", "status": "pending", "created_at": "2025-06-16T00:24:42.403189", "completed_at": null}, {"id": "task-89faf51a", "priority": 4, "description": "Deploy to staging environment", "status": "pending", "created_at": "2025-06-16T00:24:42.403365", "completed_at": null}, {"id": "task-3ec45bf9", "priority": 5, "description": "Update user documentation", "status": "pending", "created_at": "2025-06-16T00:24:42.403489", "completed_at": null}, {"id": "task-1e576364", "priority": 2, "description": "New test task", "status": "pending", "created_at": "2025-06-16T01:32:16.904875", "completed_at": null}, {"id": "task-b7fedae4", "priority": 2, "description": "Task A", "status": "pending", "created_at": "2025-06-16T01:45:10.101877", "completed_at": null}, {"id": "task-f46104c8", "priority": 1, "description": "Test task while daemon running", "status": "pending", "created_at": "2025-06-16T01:54:44.307068", "completed_at": null}], "last_updated": "2025-06-16T01:54:44.307081"}