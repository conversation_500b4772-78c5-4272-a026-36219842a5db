# Productivity Guard Usage Guide

## 🚀 Quick Start

### Method 1: Background Monitoring (Recommended)

```bash
# Start monitoring in the background
uv run python run_daemon.py start

# Check if it's running
uv run python run_daemon.py status

# In the same or different terminal, manage tasks:
uv run python main.py tasks --add "Write documentation"
uv run python main.py tasks --add "Review pull request" --priority P1
uv run python main.py tasks --list
uv run python main.py tasks --complete task-123

# Check your productivity
uv run python main.py report

# Stop monitoring
uv run python run_daemon.py stop
```

### Method 2: Interactive Mode

```bash
# Terminal 1: Start monitoring (takes over terminal)
uv run python main.py start

# Terminal 2: Manage tasks
uv run python main.py tasks --list
uv run python main.py tasks --add "New task"
```

### Method 3: Quick Demo

```bash
# Run a self-contained demo
uv run python demo_quick.py
```

## 📋 Task Management

### Add Tasks
```bash
# Add with default priority (P2)
uv run python main.py tasks --add "Task description"

# Add with specific priority
uv run python main.py tasks --add "Urgent bug fix" --priority P1
```

### View Tasks
```bash
# List all tasks
uv run python main.py tasks --list

# Check system status
uv run python main.py status
```

### Complete Tasks
```bash
# Mark task as complete
uv run python main.py tasks --complete task-abc123
```

## 🔍 When You're Off-Task

When the AI detects you're off-task, you'll see:

```
============================================================
PRODUCTIVITY CHECK
============================================================
Time: 10:45 AM

Detected Activity: Browsing social media
Expected Task: Write documentation

Please explain why you switched tasks:
> [Type your justification here]
```

Valid justifications include:
- "Taking a quick break"
- "Researching related information"
- "Waiting for build to complete"
- "Team member asked urgent question"

## 📊 Reports

```bash
# Generate productivity report
uv run python main.py report

# Shows:
# - Screenshots captured
# - Tasks completed
# - Time on/off task
# - Escalation events
```

## ⚙️ Configuration

```bash
# Create config file
uv run python main.py config --create

# Show current config
uv run python main.py config --show

# Edit config
uv run python main.py config --edit screenshot_interval=60
```

## 🎯 Best Practices

1. **Start your day**: Add your top 3-5 tasks
2. **Prioritize**: Use P1 for must-do today, P2 for should-do, P3 for nice-to-do
3. **Run monitoring**: Start the daemon when you begin focused work
4. **Be honest**: When prompted, give real justifications
5. **Review daily**: Check your report at end of day

## 🔧 Troubleshooting

### "Already running" error
```bash
uv run python run_daemon.py status  # Check status
uv run python run_daemon.py stop    # Force stop
uv run python run_daemon.py start   # Restart
```

### View logs
```bash
tail -f output/logs/daemon.log           # Daemon logs
tail -f output/logs/productivity_*.log   # App logs
```

### Reset everything
```bash
uv run python run_daemon.py stop
rm productivity_guard.pid
rm -rf output/
uv run python main.py start
```