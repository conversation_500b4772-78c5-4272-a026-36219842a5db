#!/usr/bin/env python3
"""
Productivity Guard - AI-driven focus companion.

A comprehensive productivity monitoring system that helps users stay focused
on their tasks using AI-powered screenshot analysis and adaptive interventions.
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

import typer
from dotenv import load_dotenv
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint

from src.interfaces import SystemConfig, TaskPriority
from src.main_app import ProductivityGuardApp
from src.task_manager.manager import TaskManager

# Initialize Typer app
app = typer.Typer(
    name="productivity-guard",
    help="AI-driven productivity monitoring system",
    add_completion=True
)

# Console for rich output
console = Console()

# Global app instance
_app_instance: Optional[ProductivityGuardApp] = None


def load_config(config_file: Optional[Path] = None) -> SystemConfig:
    """Load configuration from file or use defaults."""
    def process_config_data(config_data: dict) -> dict:
        """Convert string paths to Path objects."""
        if 'output_dir' in config_data:
            config_data['output_dir'] = Path(config_data['output_dir'])
        return config_data
    
    if config_file and config_file.exists():
        with open(config_file) as f:
            config_data = json.load(f)
            return SystemConfig(**process_config_data(config_data))
    
    # Check for default config file
    default_config = Path("config.json")
    if default_config.exists():
        with open(default_config) as f:
            config_data = json.load(f)
            return SystemConfig(**process_config_data(config_data))
    
    # Return default config
    return SystemConfig()


def setup_logging(log_level: str = "INFO", log_file: Optional[Path] = None):
    """Set up logging configuration."""
    log_dir = Path("./output/logs")
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Default log file with timestamp
    if log_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"productivity_guard_{timestamp}.log"
    
    handlers = [logging.StreamHandler()]
    if log_file:
        handlers.append(logging.FileHandler(log_file))
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=handlers
    )


@app.command()
def start(
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Path to configuration file"),
    log_level: str = typer.Option("INFO", "--log-level", "-l", help="Logging level"),
    demo: bool = typer.Option(False, "--demo", "-d", help="Run in demo mode with sample tasks")
):
    """Start the productivity monitoring system."""
    global _app_instance
    
    # Load environment variables
    load_dotenv()
    
    # Set up logging
    setup_logging(log_level)
    
    # Load configuration
    config = load_config(config_file)
    
    # Display startup banner
    console.print(Panel.fit(
        "[bold cyan]Productivity Guard[/bold cyan]\n"
        "[dim]AI-driven focus companion[/dim]",
        border_style="cyan"
    ))
    
    console.print(f"\n[green]Starting monitoring system...[/green]")
    console.print(f"Screenshot interval: {config.screenshot_interval}s")
    console.print(f"AI check interval: {config.main_ai_interval_one}s")
    console.print(f"Output directory: {config.output_dir}")
    
    # Create app instance
    _app_instance = ProductivityGuardApp(config)
    
    # Load demo tasks if requested
    if demo:
        console.print("\n[yellow]Loading demo tasks...[/yellow]")
        _app_instance.task_manager.add_task("Review pull request #123", TaskPriority.P1)
        _app_instance.task_manager.add_task("Implement user authentication", TaskPriority.P1)
        _app_instance.task_manager.add_task("Write unit tests for API endpoints", TaskPriority.P2)
        _app_instance.task_manager.add_task("Update project documentation", TaskPriority.P3)
        _app_instance.task_manager.add_task("Refactor database queries", TaskPriority.P2)
    
    console.print("\n[green]System ready![/green] Press Ctrl+C to stop.\n")
    
    try:
        # Run the app
        asyncio.run(_app_instance.start())
    except KeyboardInterrupt:
        console.print("\n[yellow]Shutting down...[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Error: {e}[/red]")
        raise typer.Exit(1)
    finally:
        _app_instance = None
        console.print("[green]Shutdown complete.[/green]")


@app.command()
def stop():
    """Stop the productivity monitoring system."""
    global _app_instance
    
    if _app_instance and _app_instance.running:
        console.print("[yellow]Stopping productivity guard...[/yellow]")
        asyncio.run(_app_instance.shutdown())
        _app_instance = None
        console.print("[green]System stopped.[/green]")
    else:
        console.print("[yellow]System is not currently running.[/yellow]")


@app.command()
def status():
    """Show current system status."""
    global _app_instance
    
    console.print(Panel.fit("[bold]System Status[/bold]", border_style="cyan"))
    
    # Check for daemon
    pid_file = Path("productivity_guard.pid")
    if pid_file.exists():
        try:
            pid = int(pid_file.read_text())
            os.kill(pid, 0)  # Check if process exists
            console.print(f"\n[green]● System is running[/green] (Daemon PID: {pid})")
            _is_running = True
        except (ProcessLookupError, ValueError):
            console.print("\n[red]● System is not running[/red] (stale PID file)")
            _is_running = False
    elif _app_instance and _app_instance.running:
        console.print("\n[green]● System is running[/green] (interactive mode)")
        _is_running = True
    else:
        console.print("\n[red]● System is not running[/red]")
        _is_running = False
    
    if _is_running and _app_instance:
        
        # Show current task
        current_task = _app_instance.task_manager.get_current_task()
        if current_task:
            console.print(f"\nCurrent task: [cyan]{current_task.description}[/cyan]")
            console.print(f"Priority: {current_task.priority.value}")
        
        # Show escalation state
        escalation_state = _app_instance.escalation_manager.get_current_state()
        console.print(f"\nEscalation level: [yellow]{escalation_state.level.name}[/yellow]")
        console.print(f"Consecutive off-task: {escalation_state.consecutive_off_task}")
        
    elif not _is_running:
        # Check for existing tasks
        task_file = Path("./output/tasks/tasks.json")
        if task_file.exists():
            with open(task_file) as f:
                data = json.load(f)
                tasks = data.get('tasks', [])
                if tasks:
                    console.print(f"\n{len(tasks)} tasks loaded from previous session")


@app.command()
def tasks(
    add: Optional[str] = typer.Option(None, "--add", "-a", help="Add a new task"),
    priority: str = typer.Option("P2", "--priority", "-p", help="Task priority (P1, P2, P3)"),
    complete: Optional[str] = typer.Option(None, "--complete", "-c", help="Mark task as complete by ID"),
    list_all: bool = typer.Option(True, "--list", "-l", help="List all tasks")
):
    """Manage tasks."""
    # Load or create task manager
    from src.task_manager.config import TaskManagerConfig
    task_config = TaskManagerConfig(tasks_file=Path("./output/tasks/tasks.json"))
    task_manager = TaskManager(task_config)
    
    # Add new task
    if add:
        try:
            task_priority = TaskPriority[priority]
            task = task_manager.add_task(add, task_priority)
            console.print(f"[green]✓ Added task:[/green] {task.description} [{task.priority.value}]")
        except KeyError:
            console.print(f"[red]Invalid priority: {priority}. Use P1, P2, or P3.[/red]")
            raise typer.Exit(1)
    
    # Complete task
    if complete:
        try:
            task_manager.complete_task(complete)
            console.print(f"[green]✓ Completed task {complete}[/green]")
        except ValueError as e:
            console.print(f"[red]Error: {e}[/red]")
            raise typer.Exit(1)
    
    # List tasks
    if list_all or (not add and not complete):
        all_tasks = task_manager.get_all_tasks()
        if not all_tasks:
            console.print("[yellow]No tasks found.[/yellow]")
            return
        
        # Create table
        table = Table(title="Tasks", show_header=True, header_style="bold cyan")
        table.add_column("ID", style="dim", width=8)
        table.add_column("Description", min_width=30)
        table.add_column("Priority", justify="center", width=10)
        table.add_column("Status", justify="center", width=12)
        table.add_column("Created", width=20)
        
        for task in all_tasks:
            status_color = "green" if task.status.value == "completed" else "yellow" if task.status.value == "in_progress" else "white"
            table.add_row(
                task.id[:8],
                task.description,
                f"[bold]{task.priority.value}[/bold]",
                f"[{status_color}]{task.status.value}[/{status_color}]",
                task.created_at.strftime("%Y-%m-%d %H:%M")
            )
        
        console.print(table)


@app.command()
def demo():
    """Run a scripted demo showing all features."""
    console.print(Panel.fit(
        "[bold cyan]Productivity Guard Demo[/bold cyan]\n"
        "[dim]Demonstrating all system features[/dim]",
        border_style="cyan"
    ))
    
    console.print("\n[yellow]Starting demo mode...[/yellow]")
    console.print("This will run a scripted demonstration of:")
    console.print("  • Screenshot capture")
    console.print("  • Task management") 
    console.print("  • AI analysis")
    console.print("  • Off-task detection")
    console.print("  • Justification flow")
    console.print("  • Escalation system")
    
    # Import and run demo
    try:
        from demo_scenario import run_demo
        asyncio.run(run_demo())
    except ImportError:
        console.print("\n[red]Demo module not found. Creating demo...[/red]")
        # Start in demo mode instead
        start(demo=True)


@app.command()
def test():
    """Run comprehensive system tests."""
    console.print("[cyan]Running system tests...[/cyan]\n")
    
    try:
        from test_full_system import main as run_tests
        asyncio.run(run_tests())
    except ImportError:
        console.print("[red]Test module not found.[/red]")
        raise typer.Exit(1)


@app.command()
def config(
    show: bool = typer.Option(False, "--show", "-s", help="Show current configuration"),
    create: bool = typer.Option(False, "--create", "-c", help="Create default config file"),
    edit: Optional[str] = typer.Option(None, "--edit", "-e", help="Edit config value (key=value)")
):
    """Manage system configuration."""
    config_file = Path("config.json")
    
    if show:
        if config_file.exists():
            with open(config_file) as f:
                config_data = json.load(f)
                console.print(Panel.fit(
                    json.dumps(config_data, indent=2),
                    title="Current Configuration",
                    border_style="cyan"
                ))
        else:
            console.print("[yellow]No configuration file found. Using defaults.[/yellow]")
            config = SystemConfig()
            console.print(Panel.fit(
                json.dumps(config.dict(), indent=2, default=str),
                title="Default Configuration",
                border_style="cyan"
            ))
    
    if create:
        if config_file.exists():
            if not typer.confirm("Configuration file exists. Overwrite?"):
                raise typer.Exit()
        
        config = SystemConfig()
        with open(config_file, 'w') as f:
            json.dump(config.dict(), f, indent=2, default=str)
        console.print(f"[green]Created configuration file: {config_file}[/green]")
    
    if edit:
        try:
            key, value = edit.split('=', 1)
            
            # Load existing config or create new
            if config_file.exists():
                with open(config_file) as f:
                    config_data = json.load(f)
            else:
                config_data = SystemConfig().dict()
            
            # Update value (attempt to parse as int/float/bool)
            try:
                if value.lower() in ('true', 'false'):
                    value = value.lower() == 'true'
                elif '.' in value:
                    value = float(value)
                else:
                    value = int(value)
            except ValueError:
                pass  # Keep as string
            
            config_data[key] = value
            
            # Save updated config
            with open(config_file, 'w') as f:
                json.dump(config_data, f, indent=2, default=str)
            
            console.print(f"[green]Updated {key} = {value}[/green]")
            
        except ValueError:
            console.print("[red]Invalid format. Use: --edit key=value[/red]")
            raise typer.Exit(1)


@app.command()
def report(
    output_dir: Path = typer.Option(Path("./output"), "--dir", "-d", help="Output directory to analyze"),
    last_n_days: int = typer.Option(7, "--days", "-n", help="Number of days to include in report")
):
    """Generate productivity report from captured data."""
    console.print(Panel.fit("[bold]Productivity Report[/bold]", border_style="cyan"))
    
    # Check for data
    if not output_dir.exists():
        console.print("[red]Output directory not found.[/red]")
        raise typer.Exit(1)
    
    # Count screenshots
    screenshot_count = len(list((output_dir / "screenshots").rglob("*.png"))) if (output_dir / "screenshots").exists() else 0
    
    # Count analysis logs
    log_count = len(list((output_dir / "logs").rglob("*.jsonl"))) if (output_dir / "logs").exists() else 0
    
    # Load tasks
    task_file = output_dir / "tasks" / "tasks.json"
    task_count = 0
    completed_tasks = 0
    if task_file.exists():
        with open(task_file) as f:
            tasks = json.load(f)
            task_list = tasks.get('tasks', [])
            task_count = len(task_list)
            completed_tasks = sum(1 for t in task_list if t.get('status') == 'completed')
    
    # Display report
    console.print(f"\nData from: [cyan]{output_dir}[/cyan]")
    console.print(f"Period: Last {last_n_days} days\n")
    
    table = Table(show_header=True, header_style="bold cyan")
    table.add_column("Metric", style="dim", min_width=20)
    table.add_column("Value", justify="right")
    
    table.add_row("Screenshots Captured", str(screenshot_count))
    table.add_row("Analysis Logs", str(log_count))
    table.add_row("Total Tasks", str(task_count))
    table.add_row("Completed Tasks", str(completed_tasks))
    
    if task_count > 0:
        completion_rate = (completed_tasks / task_count) * 100
        table.add_row("Completion Rate", f"{completion_rate:.1f}%")
    
    console.print(table)


def main():
    """Main entry point."""
    # Check Python version
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required.")
        sys.exit(1)
    
    # Run the CLI app
    app()


if __name__ == "__main__":
    main()