#!/usr/bin/env python3
"""Simple demo script to show how to use the Productivity Guard task system."""

from src.task_manager.manager import TaskManager
from src.task_manager.config import TaskManagerConfig
from src.interfaces import SystemConfig, TaskPriority
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

console = Console()

def print_banner():
    """Print demo banner."""
    banner = Panel.fit(
        "[bold blue]Productivity Guard - Task Demo[/bold blue]\n[dim]Demonstrating task management features[/dim]",
        border_style="blue"
    )
    console.print(banner)

def demo_task_management():
    """Demonstrate task management functionality."""
    print_banner()
    
    # Initialize task manager
    console.print("\n[bold]1. Initializing Task Manager...[/bold]")
    config = SystemConfig()
    task_config = TaskManagerConfig(tasks_file=config.output_dir / 'tasks.json')
    task_manager = TaskManager(config=task_config)
    console.print(f"✓ Task manager initialized with storage: {task_config.tasks_file}")
    
    # Clear existing tasks for demo
    task_manager._tasks.clear()
    task_manager._save_tasks()
    
    # Add demo tasks
    console.print("\n[bold]2. Adding Demo Tasks...[/bold]")
    demo_tasks = [
        ("Complete PRD verification and testing", TaskPriority.P1),
        ("Review system architecture", TaskPriority.P2),
        ("Write comprehensive documentation", TaskPriority.P2),
        ("Test all system components", TaskPriority.P3),
        ("Prepare deployment checklist", TaskPriority.P3),
    ]
    
    added_tasks = []
    for desc, priority in demo_tasks:
        task = task_manager.add_task(desc, priority=priority)
        added_tasks.append(task)
        console.print(f"  ✓ Added: {desc} (P{priority.value})")
    
    # Display all tasks
    console.print("\n[bold]3. Current Task List:[/bold]")
    display_tasks(task_manager)
    
    # Get current priority task
    console.print("\n[bold]4. Current Priority Task:[/bold]")
    current_task = task_manager.get_current_task()
    if current_task:
        console.print(f"  → {current_task.description} (P{current_task.priority.value})")
        console.print(f"    Status: {current_task.status.value}")
        console.print(f"    Created: {current_task.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Mark a task as complete
    if added_tasks:
        console.print("\n[bold]5. Completing First Task...[/bold]")
        first_task = added_tasks[0]
        completed_task = task_manager.mark_complete(first_task.id)
        console.print(f"  ✓ Completed: {completed_task.description}")
        console.print(f"    Completed at: {completed_task.completed_at.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Display updated task list
    console.print("\n[bold]6. Updated Task List:[/bold]")
    display_tasks(task_manager)
    
    # Show task statistics
    console.print("\n[bold]7. Task Statistics:[/bold]")
    all_tasks = task_manager.get_all_tasks()
    completed_tasks = [t for t in all_tasks if t.status.value == "completed"]
    pending_tasks = [t for t in all_tasks if t.status.value == "pending"]
    
    console.print(f"  Total tasks: {len(all_tasks)}")
    console.print(f"  Completed: {len(completed_tasks)}")
    console.print(f"  Pending: {len(pending_tasks)}")
    
    console.print("\n[green]✓ Demo completed successfully![/green]")
    console.print(f"[dim]Tasks saved to: {task_config.tasks_file}[/dim]")

def display_tasks(task_manager):
    """Display tasks in a nice table."""
    all_tasks = task_manager.get_all_tasks()
    
    if not all_tasks:
        console.print("  [yellow]No tasks found[/yellow]")
        return
    
    table = Table(title="Task List")
    table.add_column("Priority", style="cyan", no_wrap=True)
    table.add_column("Description", style="white")
    table.add_column("Status", style="green")
    table.add_column("Created", style="dim")
    
    for task in all_tasks:
        status = "✓ Complete" if task.status.value == "completed" else "○ Pending"
        created = task.created_at.strftime('%m/%d %H:%M')
        table.add_row(f"P{task.priority.value}", task.description, status, created)
    
    console.print(table)

def add_custom_task():
    """Add a custom task interactively."""
    console.print("\n[bold]Adding Custom Task:[/bold]")
    
    # Get task description
    description = console.input("Enter task description: ")
    if not description.strip():
        console.print("[red]Task description cannot be empty[/red]")
        return
    
    # Get priority
    console.print("Select priority:")
    console.print("  1 - P1 (Highest)")
    console.print("  2 - P2 (High)")
    console.print("  3 - P3 (Medium)")
    console.print("  4 - P4 (Low)")
    console.print("  5 - P5 (Lowest)")
    
    try:
        priority_num = int(console.input("Priority (1-5): "))
        if priority_num < 1 or priority_num > 5:
            raise ValueError()
        priority = TaskPriority(priority_num)
    except (ValueError, TypeError):
        console.print("[red]Invalid priority. Using P2 (High)[/red]")
        priority = TaskPriority.P2
    
    # Add the task
    config = SystemConfig()
    task_config = TaskManagerConfig(tasks_file=config.output_dir / 'tasks.json')
    task_manager = TaskManager(config=task_config)
    
    task = task_manager.add_task(description.strip(), priority=priority)
    console.print(f"[green]✓ Added task: {task.description} (P{task.priority.value})[/green]")
    
    # Show updated task list
    console.print("\n[bold]Updated Task List:[/bold]")
    display_tasks(task_manager)

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "add":
        add_custom_task()
    else:
        demo_task_management()
