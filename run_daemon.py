#!/usr/bin/env python
"""Run Productivity Guard in daemon mode with a control script."""

import subprocess
import sys
import time
import os
import signal
from pathlib import Path

PID_FILE = Path("productivity_guard.pid")

def start_daemon():
    """Start the monitoring system in the background."""
    if PID_FILE.exists():
        print("❌ Productivity Guard is already running!")
        print(f"   PID file exists: {PID_FILE}")
        return
    
    # Start the process in the background
    process = subprocess.Popen(
        [sys.executable, "main.py", "start"],
        stdout=open("output/logs/daemon.log", "a"),
        stderr=subprocess.STDOUT,
        preexec_fn=os.setsid  # Create new process group
    )
    
    # Save the PID
    PID_FILE.write_text(str(process.pid))
    print(f"✅ Productivity Guard started (PID: {process.pid})")
    print("   Logs: output/logs/daemon.log")

def stop_daemon():
    """Stop the monitoring system."""
    if not PID_FILE.exists():
        print("❌ Productivity Guard is not running!")
        return
    
    try:
        pid = int(PID_FILE.read_text())
        os.killpg(os.getpgid(pid), signal.SIGTERM)
        PID_FILE.unlink()
        print(f"✅ Productivity Guard stopped (PID: {pid})")
    except ProcessLookupError:
        PID_FILE.unlink()
        print("⚠️  Process not found, cleaned up PID file")
    except Exception as e:
        print(f"❌ Error stopping daemon: {e}")

def status_daemon():
    """Check if the daemon is running."""
    if not PID_FILE.exists():
        print("❌ Productivity Guard is not running")
        return
    
    try:
        pid = int(PID_FILE.read_text())
        os.kill(pid, 0)  # Check if process exists
        print(f"✅ Productivity Guard is running (PID: {pid})")
        
        # Show recent log entries
        log_file = Path("output/logs/daemon.log")
        if log_file.exists():
            print("\nRecent activity:")
            lines = log_file.read_text().splitlines()
            for line in lines[-5:]:
                print(f"   {line}")
    except ProcessLookupError:
        PID_FILE.unlink()
        print("❌ Productivity Guard is not running (stale PID file removed)")

def usage():
    """Show usage information."""
    print("""
Productivity Guard Daemon Control
=================================

Usage:
    python run_daemon.py start    - Start monitoring in background
    python run_daemon.py stop     - Stop monitoring
    python run_daemon.py status   - Check if running
    python run_daemon.py restart  - Restart monitoring

While running, use these commands in another terminal:
    python main.py tasks --add "Task name"
    python main.py tasks --list
    python main.py status
    python main.py report
""")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        usage()
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "start":
        start_daemon()
    elif command == "stop":
        stop_daemon()
    elif command == "status":
        status_daemon()
    elif command == "restart":
        stop_daemon()
        time.sleep(1)
        start_daemon()
    else:
        print(f"Unknown command: {command}")
        usage()
        sys.exit(1)