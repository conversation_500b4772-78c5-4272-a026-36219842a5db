"""
Data contracts and interfaces for the Productivity Guard system.

This module defines all the data types and abstract interfaces that flow between
the various components of the system, ensuring type safety and clear contracts.
"""

from __future__ import annotations

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, TypedDict


# ─────────────────────────────────────────────────────────────────────────────
# Data Types (TypedDict/dataclass)
# ─────────────────────────────────────────────────────────────────────────────


class ScreenshotData(TypedDict):
    """
    Data structure for screenshot information.
    
    Example:
        {
            "path": "/output/screenshots/2025JUN15/000159-023700-0001.png",
            "timestamp": "2025-06-15T00:01:59.023700",
            "screen_id": 1
        }
    """
    path: str  # Absolute path to the screenshot file
    timestamp: str  # ISO format timestamp when screenshot was taken
    screen_id: int  # Display/monitor ID (1-based)


class TaskStatus(str, Enum):
    """Valid statuses for a task."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    BLOCKED = "blocked"


class TaskPriority(int, Enum):
    """Task priority levels (1 is highest)."""
    P1 = 1
    P2 = 2
    P3 = 3
    P4 = 4
    P5 = 5


@dataclass
class TaskData:
    """
    Data structure for task information.
    
    Example:
        task = TaskData(
            id="task-001",
            priority=TaskPriority.P1,
            description="Implement screenshot capture module",
            status=TaskStatus.IN_PROGRESS,
            created_at=datetime.now()
        )
    """
    id: str  # Unique task identifier
    priority: TaskPriority  # Priority level (1-5, 1 being highest)
    description: str  # Human-readable task description
    status: TaskStatus  # Current task status
    created_at: datetime  # When the task was created
    completed_at: Optional[datetime] = None  # When task was completed (if applicable)
    
    def __post_init__(self):
        """Validate task data."""
        if self.status == TaskStatus.COMPLETED and self.completed_at is None:
            self.completed_at = datetime.now()


@dataclass
class AIAnalysisResult:
    """
    Result from AI reasoning about whether user is on-task.
    
    Example:
        result = AIAnalysisResult(
            on_task=False,
            confidence=0.85,
            reason="User browsing Reddit while task is 'Code review'",
            screenshot_ref="/output/screenshots/2025JUN15/000159-023700-0001.png"
        )
    """
    on_task: bool  # Whether user is working on current priority task
    confidence: float  # Confidence score (0.0 to 1.0)
    reason: str  # Human-readable explanation of the decision
    screenshot_ref: str  # Path to the analyzed screenshot
    
    def __post_init__(self):
        """Validate confidence is in valid range."""
        if not 0.0 <= self.confidence <= 1.0:
            raise ValueError(f"Confidence must be between 0 and 1, got {self.confidence}")


@dataclass
class JustificationRequest:
    """
    Request sent to user when off-task behavior is detected.
    
    Example:
        request = JustificationRequest(
            timestamp=datetime.now(),
            detected_activity="Browsing social media (Twitter)",
            expected_task="Implement unit tests for auth module"
        )
    """
    timestamp: datetime  # When the off-task behavior was detected
    detected_activity: str  # What the AI thinks the user is doing
    expected_task: str  # What the user should be doing (Priority #1)


@dataclass
class JustificationResponse:
    """
    User's response to a justification request.
    
    Example:
        response = JustificationResponse(
            user_input="Checking Twitter for API outage updates",
            valid=True,
            timestamp=datetime.now()
        )
    """
    user_input: str  # User's justification text
    valid: bool  # Whether the justification was accepted
    timestamp: datetime  # When the response was received


class EscalationLevel(int, Enum):
    """Escalation levels for intervention."""
    NORMAL = 0  # Normal monitoring mode
    WARNING = 1  # First warning issued
    FREQUENT = 2  # Increased monitoring frequency
    AGGRESSIVE = 3  # Tab closing intervention


@dataclass
class EscalationState:
    """
    Current state of the escalation system.
    
    Example:
        state = EscalationState(
            level=EscalationLevel.WARNING,
            started_at=datetime.now(),
            prompt_count=2
        )
    """
    level: EscalationLevel  # Current escalation level
    started_at: datetime  # When this escalation level started
    prompt_count: int = 0  # Number of prompts sent at this level
    
    def should_escalate(self, max_prompts: int = 3) -> bool:
        """Check if we should escalate to next level."""
        return self.prompt_count >= max_prompts


# ─────────────────────────────────────────────────────────────────────────────
# Abstract Base Classes (Module Interfaces)
# ─────────────────────────────────────────────────────────────────────────────


class ScreenshotCaptureInterface(ABC):
    """
    Interface for screenshot capture module.
    
    Data flow: None -> ScreenshotData
    """
    
    @abstractmethod
    async def capture_screenshot(self, screen_id: int) -> ScreenshotData:
        """
        Capture a screenshot from the specified screen.
        
        Args:
            screen_id: Display/monitor ID to capture (1-based)
            
        Returns:
            ScreenshotData with path, timestamp, and screen_id
            
        Raises:
            ScreenNotFoundError: If screen_id doesn't exist
            CaptureError: If screenshot capture fails
        """
        pass
    
    @abstractmethod
    def get_available_screens(self) -> List[int]:
        """Get list of available screen IDs."""
        pass


class TaskManagerInterface(ABC):
    """
    Interface for task management module.
    
    Data flow: TaskData <-> Storage
    """
    
    @abstractmethod
    def get_current_task(self) -> Optional[TaskData]:
        """
        Get the current highest priority incomplete task.
        
        Returns:
            TaskData for current task or None if no tasks
        """
        pass
    
    @abstractmethod
    def get_all_tasks(self) -> List[TaskData]:
        """Get all tasks ordered by priority."""
        pass
    
    @abstractmethod
    def add_task(self, description: str, priority: TaskPriority) -> TaskData:
        """Add a new task."""
        pass
    
    @abstractmethod
    def update_task_status(self, task_id: str, status: TaskStatus) -> TaskData:
        """Update task status."""
        pass
    
    @abstractmethod
    def import_tasks(self, task_list: List[str]) -> List[TaskData]:
        """Import tasks from a simple string list."""
        pass


class AIReasoningInterface(ABC):
    """
    Interface for AI reasoning module.
    
    Data flow: (ScreenshotData, TaskData) -> AIAnalysisResult
    """
    
    @abstractmethod
    async def analyze_screenshot(
        self, 
        screenshot: ScreenshotData, 
        current_task: TaskData
    ) -> AIAnalysisResult:
        """
        Analyze screenshot to determine if user is on-task.
        
        Args:
            screenshot: Screenshot data to analyze
            current_task: The task user should be working on
            
        Returns:
            AIAnalysisResult with decision and reasoning
        """
        pass
    
    @abstractmethod
    def set_confidence_threshold(self, threshold: float) -> None:
        """Set minimum confidence for off-task determination."""
        pass


class UserInterfaceInterface(ABC):
    """
    Interface for user interaction module.
    
    Data flow: JustificationRequest -> JustificationResponse
    """
    
    @abstractmethod
    async def request_justification(
        self, 
        request: JustificationRequest
    ) -> Optional[JustificationResponse]:
        """
        Request justification from user for off-task behavior.
        
        Args:
            request: Details about the detected off-task behavior
            
        Returns:
            JustificationResponse if user responds, None if timeout
        """
        pass
    
    @abstractmethod
    def send_notification(self, message: str, urgency: str = "normal") -> None:
        """Send notification to user."""
        pass


class EscalationManagerInterface(ABC):
    """
    Interface for escalation management module.
    
    Data flow: (AIAnalysisResult, JustificationResponse?) -> EscalationState
    """
    
    @abstractmethod
    def get_current_state(self) -> EscalationState:
        """Get current escalation state."""
        pass
    
    @abstractmethod
    def process_analysis(
        self, 
        analysis: AIAnalysisResult,
        justification: Optional[JustificationResponse] = None
    ) -> EscalationState:
        """
        Process analysis result and update escalation state.
        
        Args:
            analysis: AI analysis result
            justification: User's justification if provided
            
        Returns:
            Updated escalation state
        """
        pass
    
    @abstractmethod
    def execute_intervention(self) -> None:
        """Execute intervention based on current escalation level."""
        pass
    
    @abstractmethod
    def reset_escalation(self) -> None:
        """Reset escalation to normal mode."""
        pass


class LocalStorageInterface(ABC):
    """
    Interface for local storage module.
    
    Data flow: Various data types <-> Persistent storage
    """
    
    @abstractmethod
    def save_screenshot_metadata(self, screenshot: ScreenshotData) -> None:
        """Save screenshot metadata."""
        pass
    
    @abstractmethod
    def save_analysis_log(
        self, 
        analysis: AIAnalysisResult,
        justification: Optional[JustificationResponse] = None
    ) -> None:
        """Log analysis result and any justification."""
        pass
    
    @abstractmethod
    def save_task_list(self, tasks: List[TaskData]) -> None:
        """Persist task list."""
        pass
    
    @abstractmethod
    def load_task_list(self) -> List[TaskData]:
        """Load persisted task list."""
        pass
    
    @abstractmethod
    def get_activity_summary(
        self, 
        start_date: datetime, 
        end_date: datetime
    ) -> Dict[str, Any]:
        """Get activity summary for date range."""
        pass


# ─────────────────────────────────────────────────────────────────────────────
# Data Flow Pipeline
# ─────────────────────────────────────────────────────────────────────────────
"""
Data Flow Through the System:

1. Screenshot Capture:
   ScreenshotCaptureInterface.capture_screenshot() -> ScreenshotData

2. Task Management:
   TaskManagerInterface.get_current_task() -> TaskData

3. AI Analysis:
   AIReasoningInterface.analyze_screenshot(ScreenshotData, TaskData) -> AIAnalysisResult

4. Decision Point:
   If AIAnalysisResult.on_task == False:
     
     4a. Request Justification:
         UserInterfaceInterface.request_justification(JustificationRequest) -> JustificationResponse?
     
     4b. Process Escalation:
         EscalationManagerInterface.process_analysis(AIAnalysisResult, JustificationResponse?) -> EscalationState
     
     4c. Execute Intervention (if needed):
         EscalationManagerInterface.execute_intervention()

5. Logging:
   LocalStorageInterface.save_analysis_log(AIAnalysisResult, JustificationResponse?)

The system continuously cycles through steps 1-5 at intervals defined by the current EscalationState.
"""


# ─────────────────────────────────────────────────────────────────────────────
# Configuration Interface
# ─────────────────────────────────────────────────────────────────────────────


@dataclass
class SystemConfig:
    """
    System-wide configuration matching PRD requirements.
    
    Example:
        config = SystemConfig(
            screenshot_interval=5,
            main_ai_interval_one=5,
            main_ai_interval_two=1,
            screens_to_capture=[1, 2, 3, 4]
        )
    """
    screenshot_interval: int = 120  # seconds between screenshots
    main_ai_interval_one: int = 300  # normal mode AI check interval (seconds)  
    main_ai_interval_two: int = 120  # escalated mode AI check interval (seconds)
    screens_to_capture: List[int] = field(default_factory=lambda: [1, 2, 3, 4])
    justification_timeout: int = 60  # seconds to wait for justification
    escalation_prompt_threshold: int = 3  # prompts before escalating
    output_dir: Path = field(default_factory=lambda: Path("./output"))
    log_level: str = "INFO"
    
    def __post_init__(self):
        """Ensure output_dir is always a Path object."""
        if isinstance(self.output_dir, str):
            self.output_dir = Path(self.output_dir)
    
    def dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "screenshot_interval": self.screenshot_interval,
            "main_ai_interval_one": self.main_ai_interval_one,
            "main_ai_interval_two": self.main_ai_interval_two,
            "screens_to_capture": self.screens_to_capture,
            "justification_timeout": self.justification_timeout,
            "escalation_prompt_threshold": self.escalation_prompt_threshold,
            "output_dir": str(self.output_dir),
            "log_level": self.log_level
        }


# ─────────────────────────────────────────────────────────────────────────────
# Exception Types
# ─────────────────────────────────────────────────────────────────────────────


class ProductivityGuardError(Exception):
    """Base exception for all Productivity Guard errors."""
    pass


class ScreenNotFoundError(ProductivityGuardError):
    """Raised when requested screen ID doesn't exist."""
    pass


class CaptureError(ProductivityGuardError):
    """Raised when screenshot capture fails."""
    pass


class TaskNotFoundError(ProductivityGuardError):
    """Raised when requested task doesn't exist."""
    pass


class AIAnalysisError(ProductivityGuardError):
    """Raised when AI analysis fails."""
    pass