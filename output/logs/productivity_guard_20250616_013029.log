2025-06-16 01:30:29,818 - src.main_app - INFO - Initializing Productivity Guard components...
2025-06-16 01:30:29,818 - src.task_manager.manager - INFO - Loaded 1 tasks from output/tasks.json
2025-06-16 01:30:29,819 - src.local_store.store - INFO - LocalStore initialized with base path: output
2025-06-16 01:30:29,819 - src.main_app - INFO - All components initialized successfully
2025-06-16 01:30:29,819 - src.task_manager.manager - INFO - Added task task-157f0b77: Review pull request #123...
2025-06-16 01:30:29,819 - src.task_manager.manager - INFO - Added task task-6074489d: Implement user authentication...
2025-06-16 01:30:29,819 - src.task_manager.manager - INFO - Added task task-615e358e: Write unit tests for API endpoints...
2025-06-16 01:30:29,819 - src.task_manager.manager - INFO - Added task task-4bfd7ccb: Update project documentation...
2025-06-16 01:30:29,819 - src.task_manager.manager - INFO - Added task task-6abcfbd1: Refactor database queries...
2025-06-16 01:30:29,820 - src.main_app - INFO - Starting Productivity Guard...
2025-06-16 01:30:29,820 - src.main_app - INFO - Entering main monitoring loop
2025-06-16 01:30:30,344 - src.reasoning_ai.reasoner - INFO - Mock Analysis: OFF-TASK (confidence: 0.85)
2025-06-16 01:30:30,345 - src.main_app - WARNING - User appears off-task: Browsing social media instead of working on 'Test task from Python' (confidence: 0.85)
2025-06-16 01:30:30,811 - src.main_app - ERROR - Failed to capture screenshot from screen 3: Invalid screen_id 3, must be 1-2
2025-06-16 01:30:30,811 - src.main_app - ERROR - Failed to capture screenshot from screen 4: Invalid screen_id 4, must be 1-2
2025-06-16 01:30:39,705 - src.main_app - INFO - Received signal 15, initiating shutdown...
2025-06-16 01:31:30,351 - src.main_app - INFO - Shutting down Productivity Guard...
2025-06-16 01:31:30,353 - src.local_store.store - INFO - Saved 6 tasks to output/tasks.json
2025-06-16 01:31:30,353 - src.main_app - INFO - Shutdown complete
