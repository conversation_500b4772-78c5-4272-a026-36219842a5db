2025-06-16 01:34:20,861 - src.main_app - INFO - Initializing Productivity Guard components...
2025-06-16 01:34:20,861 - src.task_manager.manager - INFO - No existing tasks file found
2025-06-16 01:34:20,862 - src.local_store.store - INFO - LocalStore initialized with base path: output/system_test/component_test
2025-06-16 01:34:20,862 - src.main_app - INFO - All components initialized successfully
2025-06-16 01:34:20,862 - src.task_manager.manager - INFO - Added task task-6b8593e0: Complete code review for PR #123...
2025-06-16 01:34:20,862 - src.task_manager.manager - INFO - Added task task-b7075ddd: Write unit tests for new feature...
2025-06-16 01:34:20,862 - src.task_manager.manager - INFO - Added task task-92ce67f6: Update API documentation...
2025-06-16 01:34:20,862 - src.task_manager.manager - INFO - Added task task-86da772d: Refactor legacy authentication code...
2025-06-16 01:34:20,862 - src.task_manager.manager - INFO - Added task task-f6219d4a: Prepare sprint retrospective notes...
2025-06-16 01:34:24,802 - src.escalator.escalator - INFO - Resetting escalation from NORMAL to NORMAL
2025-06-16 01:34:24,803 - src.local_store.store - INFO - Saved 5 tasks to output/system_test/component_test/tasks.json
2025-06-16 01:34:24,803 - src.main_app - INFO - Initializing Productivity Guard components...
2025-06-16 01:34:24,803 - src.task_manager.manager - INFO - No existing tasks file found
2025-06-16 01:34:24,803 - src.local_store.store - INFO - LocalStore initialized with base path: output/system_test/workflow_simulation
2025-06-16 01:34:24,803 - src.main_app - INFO - All components initialized successfully
2025-06-16 01:34:24,804 - src.task_manager.manager - INFO - Added task task-e710ddda: Complete feature implementation...
2025-06-16 01:34:24,804 - src.task_manager.manager - INFO - Added task task-fac52ef8: Write comprehensive tests...
2025-06-16 01:34:24,804 - src.main_app - INFO - Starting Productivity Guard...
2025-06-16 01:34:24,804 - src.main_app - INFO - Entering main monitoring loop
2025-06-16 01:34:24,804 - src.main_app - WARNING - No recent screenshot available for analysis
2025-06-16 01:34:28,806 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 160, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:29,848 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 160, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:30,850 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 160, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:31,852 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 160, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:32,853 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 160, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:33,854 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 160, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:34,856 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 160, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:35,856 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 170, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:36,858 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 170, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:37,859 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 170, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:38,861 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 170, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:39,863 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 170, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:40,865 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 170, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:41,867 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 170, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:42,868 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 170, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:43,870 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 170, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:44,872 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 170, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:45,873 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 170, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:46,875 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 170, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:47,877 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 170, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:48,878 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 170, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
2025-06-16 01:34:49,882 - src.main_app - ERROR - Error in AI analysis loop: 'dict' object has no attribute 'path'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Code3/Feisty4/src/main_app.py", line 236, in _ai_analysis_loop
    analysis_result = await self.ai_reasoner.analyze_screenshot(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Code3/Feisty4/test_full_system.py", line 170, in analyze_screenshot
    screenshot_path=screenshot.path,
                    ^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'path'
